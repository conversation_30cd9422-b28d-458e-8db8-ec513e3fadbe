'use client';

import { cn } from "@/lib/utils";
import { Progress } from "@/components/ui/progress";
import { CheckCircle2 } from "lucide-react";
import { LoadingDots } from "@/components/ui/loading-dots";

// Define the creation steps
export const CREATION_STEPS = [
  { id: 'analyzing', label: '分析职位描述' },
  { id: 'formatting', label: '格式化要求' },
  { id: 'tailoring', label: '定制简历内容' },
  { id: 'finalizing', label: '完成简历' },
] as const;

export type CreationStep = typeof CREATION_STEPS[number]['id'];

interface LoadingOverlayProps {
  currentStep: CreationStep;
}

export function LoadingOverlay({ currentStep }: LoadingOverlayProps) {
  const currentStepIndex = CREATION_STEPS.findIndex(step => step.id === currentStep);
  const progress = ((currentStepIndex + 1) / CREATION_STEPS.length) * 100;

  return (
    <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="max-w-md w-full space-y-8 p-8">
        {/* Progress bar */}
        <div className="space-y-3">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>创建简历</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Steps */}
        <div className="space-y-4">
          {CREATION_STEPS.map((step, index) => {
            const isActive = step.id === currentStep;
            const isCompleted = index < currentStepIndex;
            
            return (
              <div
                key={step.id}
                className={cn(
                  "flex items-center gap-3 p-3 rounded-lg transition-colors duration-300",
                  isActive && "bg-pink-50 text-pink-900",
                  isCompleted && "text-muted-foreground"
                )}
              >
                {isCompleted ? (
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                ) : isActive ? (
                  <div className="h-5 w-5 flex items-center justify-center">
                    <LoadingDots className="text-pink-600" />
                  </div>
                ) : (
                  <div className="h-5 w-5 rounded-full border-2 border-muted" />
                )}
                <span className={cn(
                  "text-sm font-medium",
                  isActive && "text-pink-900",
                  !isActive && !isCompleted && "text-muted-foreground"
                )}>
                  {step.label}
                </span>
              </div>
            );
          })}
        </div>

        {/* Current action description */}
        <div className="text-center">
          <p className="text-sm text-muted-foreground animate-pulse">
            {currentStep === 'analyzing' && "正在阅读和理解职位要求..."}
            {currentStep === 'formatting' && "正在整理职位信息..."}
            {currentStep === 'tailoring' && "正在优化您的简历以获得最佳匹配..."}
            {currentStep === 'finalizing' && "正在进行最后的润色..."}
          </p>
        </div>
      </div>
    </div>
  );
} 