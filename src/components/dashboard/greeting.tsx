'use client';

import { useState, useEffect } from 'react';

function getGreeting() {
  const hour = new Date().getHours();
  if (hour < 12) return "早上好";
  if (hour < 17) return "下午好";
  return "晚上好";
}

interface GreetingProps {
  firstName: string;
}

export function Greeting({ firstName }: GreetingProps) {
  const [greeting, setGreeting] = useState("您好"); // Default fallback
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    setGreeting(getGreeting());
  }, []);

  // Show fallback during hydration
  if (!isClient) {
    return (
      <h1 className="text-2xl font-semibold text-ins-mint-600">
        您好, {firstName}
      </h1>
    );
  }

  return (
    <h1 className="text-2xl font-semibold text-ins-mint-600">
      {greeting}, {firstName}
    </h1>
  );
}
